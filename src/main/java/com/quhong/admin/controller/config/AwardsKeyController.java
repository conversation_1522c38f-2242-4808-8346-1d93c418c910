package com.quhong.admin.controller.config;

import com.quhong.admin.data.dto.config.reward.AwardsKeyAddDTO;
import com.quhong.admin.data.dto.config.reward.AwardsKeyGiveDTO;
import com.quhong.admin.data.dto.config.reward.AwardsKeyListQueryDTO;
import com.quhong.admin.data.dto.config.reward.AwardsKeyUpdateDTO;
import com.quhong.admin.data.vo.config.reward.AwardsKeyVO;
import com.quhong.admin.service.config.reward.rewardsKey.AwardsKeyService;
import com.quhong.core.common.ApiResult;
import com.quhong.core.exception.BusinessException;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.data.vo.PageResultVo;
import com.quhong.exceptions.WebException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 配置管理/礼包key配置
 *
 * <AUTHOR>
 * @since 2024/4/15
 */
@Slf4j
@RestController
@RequestMapping("/java_admin/config/awards_key")
public class AwardsKeyController {

    @Resource
    private AwardsKeyService awardsKeyService;

    /**
     * 查询礼包key列表
     *
     * @param dto 查询条件
     * @return 分页结果
     */
    @GetMapping("/list")
    public ApiResult<PageResultVo<List<AwardsKeyVO>>> listAwardsKey(AwardsKeyListQueryDTO dto) {
        try {
            PageResultVo<List<AwardsKeyVO>> result = awardsKeyService.listAwardsKey(dto);
            return ApiResult.success(result);
        } catch (Exception e) {
            log.error("查询礼包key列表异常", e);
            return ApiResult.error("查询礼包key列表失败");
        }
    }

    /**
     * 新增礼包key
     *
     * @param dto 新增参数
     * @return 是否成功
     */
    @PostMapping("/add")
    public ApiResult<Boolean> addAwardsKey(@RequestBody AwardsKeyAddDTO dto) {
        try {
            boolean result = awardsKeyService.addAwardsKey(dto);
            return ApiResult.success(result);
        } catch (BusinessException e) {
            log.error("新增礼包key业务异常: {}", e.getMessage());
            return ApiResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("新增礼包key系统异常", e);
            return ApiResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 修改礼包key
     *
     * @param dto 修改参数
     * @return 是否成功
     */
    @PostMapping("/update")
    public ApiResult<Boolean> updateAwardsKey(@RequestBody AwardsKeyUpdateDTO dto) {
        try {
            boolean result = awardsKeyService.updateAwardsKey(dto);
            return ApiResult.success(result);
        } catch (BusinessException e) {
            log.error("修改礼包key业务异常: {}", e.getMessage());
            return ApiResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("修改礼包key系统异常", e);
            return ApiResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 获取礼包key配置
     *
     * @param awardsKey 礼包key标识
     * @return 礼包key配置
     */
    @GetMapping("/detail")
    public ApiResult<AwardsKeyVO> getAwardsKey(@RequestParam("awardsKey") String awardsKey) {
        try {
            AwardsKeyVO result = awardsKeyService.getAwardsKey(awardsKey);
            if (result == null) {
                return ApiResult.error("礼包key不存在或已失效");
            }
            return ApiResult.success(result);
        } catch (Exception e) {
            log.error("获取礼包key配置异常", e);
            return ApiResult.error("获取礼包key配置失败");
        }
    }

    /**
     * 下发礼包奖励
     *
     * @param dto 下发参数
     * @return 是否成功
     */
    @PostMapping("/give")
    public ApiResult<Boolean> giveAwards(@RequestBody AwardsKeyGiveDTO dto) {
        try {
            boolean result = awardsKeyService.giveAwards(dto);
            return ApiResult.success(result);
        } catch (BusinessException e) {
            log.error("下发礼包奖励业务异常: {}", e.getMessage());
            return ApiResult.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("下发礼包奖励系统异常", e);
            return ApiResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 获取物品信息
     *
     * @param awardType 奖励类型
     * @param dataId 奖励ID
     * @return 物品信息
     */
    @GetMapping("/item_info")
    public ApiResult<AwardInfo> getItemInfo(
        @RequestParam("awardType") Integer awardType,
        @RequestParam("dataId") Integer dataId
    ) {
        try {
            AwardInfo result = awardsKeyService.getItemInfo(awardType, dataId);
            return ApiResult.success(result);
        }catch (WebException e){
            throw e;
        } catch (Exception e) {
            log.error("获取物品信息异常", e);
            return ApiResult.error("获取物品信息失败");
        }
    }
}
