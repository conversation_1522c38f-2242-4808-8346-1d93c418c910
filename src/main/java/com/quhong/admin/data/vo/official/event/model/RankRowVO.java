package com.quhong.admin.data.vo.official.event.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/3/6 10:25
 */
@Data
@Accessors(chain = true)
public class RankRowVO {
    /**
     * 排名
     */
    @ExcelProperty("排名")
    private Integer rankNum;
    /**
     * rid
     */
    @ColumnWidth(16)
    @ExcelProperty("rid")
    private Long rid;
    /**
     * uid
     */
    @ExcelIgnore
    private String uid;
    /**
     * 排行值
     */
    @ColumnWidth(16)
    @ExcelProperty("排行值")
    private Long rankCount;
}
