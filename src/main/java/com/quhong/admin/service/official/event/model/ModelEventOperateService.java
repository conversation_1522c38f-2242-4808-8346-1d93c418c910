package com.quhong.admin.service.official.event.model;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.quhong.admin.constant.PageConsts;
import com.quhong.admin.data.dto.official.activity.model.CreateEventDTO;
import com.quhong.admin.data.dto.official.activity.model.UpdateEventDTO;
import com.quhong.admin.data.vo.official.event.model.CopyModelVO;
import com.quhong.admin.data.vo.official.event.model.EventModelVO;
import com.quhong.admin.data.vo.official.event.model.RankRowVO;
import com.quhong.common.data.BaseHttpData;
import com.quhong.common.enums.HttpCode;
import com.quhong.constant.ActType;
import com.quhong.constant.activity.EventGroupConstant;
import com.quhong.core.config.ServerConfiguration;
import com.quhong.core.date.DayTimeSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.dao.ActivityCountActorInfoDao;
import com.quhong.dao.AppConfigActivityDao;
import com.quhong.dao.AppEventDao;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.datas.db.ActivityCountActorInfoData;
import com.quhong.dao.datas.db.AppEventData;
import com.quhong.data.vo.PageResultVo;
import com.quhong.exceptions.WebException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <AUTHOR>
 * @since 2024/2/2 11:30
 */
@Slf4j
@Service
public class ModelEventOperateService {

    @Resource
    private AppConfigActivityDao appConfigActivityDao;
    @Resource
    private AppEventDao appEventDao;
    @Resource
    private ActivityCountActorInfoDao activityCountActorInfoDao;

    private final String url;

    public ModelEventOperateService() {
        if (ServerConfiguration.isProduct()) {
            url = "https://videochat.kissu.site/event_template_new/?eventType=";
        } else {
            url = "https://testvideochat.kissu.site/event_template_new/?eventType=";
        }
    }


    public boolean createEvent(CreateEventDTO dto) {
        checkCreateParams(dto);
        long currTime = DateHelper.getCurrTime();
        AppEventData eventData = saveAppEventData(dto, currTime);
        saveAppConfigActivityData(dto, eventData, currTime);
        return true;
    }


    public boolean updateEvent(UpdateEventDTO dto) {
        checkUpdateParams(dto);
        return updateAppConfigActivityData(dto);
    }

    public boolean logicDelActivity(Integer id, String username, Integer eventCode) {
        if (id == null) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("id is empty"), true);
        }
        return logicDelAppConfigActivity(id, username, eventCode);
    }

    private boolean logicDelAppConfigActivity(Integer id, String username, Integer eventCode) {
        AppConfigActivityData delData = new AppConfigActivityData();
        delData.setId(id);
        delData.setActivityCode(eventCode);
        delData.setMtime(DateHelper.getCurrTime());
        delData.setValid(0);
        delData.setOperator(username);
        return appConfigActivityDao.updateOneSelective(delData);
    }

    private boolean updateAppConfigActivityData(UpdateEventDTO dto) {
        AppConfigActivityData updateData = new AppConfigActivityData();
        updateData.setId(dto.getId().intValue());
        updateData.setName(dto.getName());
        updateData.setActivityCode(dto.getEventCode());
        updateData.setActivityDesc(dto.getEventDesc());
        updateData.setZoneOffset(dto.getZoneOffset());
        updateData.setDateFormat(dto.getDateFormat());
        updateData.setStartTime(DayTimeSupport.strParseToSec(dto.getStartTime(), dto.getZoneOffset(), dto.getDateFormat()));
        updateData.setEndTime(DayTimeSupport.strParseToSec(dto.getEndTime(), dto.getZoneOffset(), dto.getDateFormat()));
        updateData.setChannelSet(dto.getChannelSet());
        updateData.setCountryCodeSet(dto.getCountryCodeSet());
        updateData.setJoinModeInfo(dto.getJoinModeInfo());
        updateData.setEventModeInfo(dto.getEventModeInfo());
        updateData.setNotJoinRidSet(dto.getNotJoinRidSet());
        updateData.setRankType(dto.getRankType());
        updateData.setPageInfo(dto.getPageInfo());
        updateData.setValid(dto.getValid());
        updateData.setOperator(dto.getOperator());
        updateData.setMtime(DateHelper.getCurrTime());
        return appConfigActivityDao.updateOneSelective(updateData);
    }

    private void checkUpdateParams(UpdateEventDTO dto) {
        if (dto.getId() == null) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("id is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getName())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("name is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getZoneOffset())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("zoneOffset is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getDateFormat())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("dateFormat is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getStartTime())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("startTime is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getEndTime())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("endTime is empty"), true);
        }
    }
    private void saveAppConfigActivityData(CreateEventDTO dto, AppEventData eventData, long currTime) {
        AppConfigActivityData configData = new AppConfigActivityData();
        configData.setActivityCode(eventData.getEventCode().intValue());
        configData.setEventGroup(eventData.getEventGroup());
        configData.setName(dto.getName());
        configData.setActivityDesc(dto.getEventDesc());
        configData.setZoneOffset(dto.getZoneOffset());
        configData.setDateFormat(dto.getDateFormat());
        configData.setStartTime(DayTimeSupport.strParseToSec(dto.getStartTime(), configData.getZoneOffset(), configData.getDateFormat()));
        configData.setEndTime(DayTimeSupport.strParseToSec(dto.getEndTime(), configData.getZoneOffset(), configData.getDateFormat()));
        configData.setActType((int) ActType.ACTIVITY_REWARD);
        configData.setChannelSet(dto.getChannelSet());
        configData.setCountryCodeSet(dto.getCountryCodeSet());
        configData.setJoinModeInfo(dto.getJoinModeInfo());
        configData.setNotJoinRidSet(dto.getNotJoinRidSet());
        configData.setEventModeInfo(dto.getEventModeInfo());
        configData.setRankType(dto.getRankType());
        configData.setPageInfo(dto.getPageInfo());
        configData.setValid(dto.getValid());
        configData.setCtime(currTime);
        configData.setMtime(currTime);
        configData.setOperator(dto.getOperator());
        configData.setUrl(url + eventData.getEventCode());
        appConfigActivityDao.insertOneSelective(configData);
    }

    private AppEventData saveAppEventData(CreateEventDTO dto, long currTime) {
        AppEventData eventData = new AppEventData();
        eventData.setCategory(1);
        eventData.setEventGroup(EventGroupConstant.MODEL_EVENT);
        eventData.setName(dto.getName());
        eventData.setEventDesc(dto.getEventDesc());
        eventData.setValid(1);
        eventData.setCtime(currTime);
        eventData.setMtime(currTime);
        eventData.setOperator(dto.getOperator());
        eventData = appEventDao.insertOneSelective(eventData);
        return eventData;
    }

    private void checkCreateParams(CreateEventDTO dto) {
        if (StringUtils.isEmpty(dto.getName())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("name is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getZoneOffset())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("zoneOffset is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getDateFormat())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("dateFormat is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getStartTime())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("startTime is empty"), true);
        }
        if (StringUtils.isEmpty(dto.getEndTime())) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("endTime is empty"), true);
        }
        if (dto.getValid() == null) {
            dto.setValid(1);
        }
    }

    public PageResultVo<List<EventModelVO>> queryList(Integer status, String zoneOffset, Integer page, Integer valid, Integer eventGroup) {
        if (page == null || page < 1) {
            page = 1;
        }
        long currTime = DateHelper.getCurrTime();
        String activeStatus = "";
        Long startGreaterThan = null, startLessThanAndEqualTo = null, endGreaterThan = null, endLessThan = null;
        switch (status) {
            case 1://未开始
                startGreaterThan = currTime;
                activeStatus = "未开始";
                break;
            case 2://进行中
                startLessThanAndEqualTo = currTime;
                endGreaterThan = currTime;
                activeStatus = "进行中";
                break;
            case 3://已结束
                endLessThan = currTime;
                activeStatus = "已结束";
                break;
            case 0://ALL
            default:
                break;
        }
        PageHelper.startPage(page, PageConsts.PAGE_SIZE_10);
        List<AppConfigActivityData> dataList = appConfigActivityDao.queryListBy(zoneOffset, valid, eventGroup,
                startGreaterThan, startLessThanAndEqualTo, endGreaterThan, endLessThan);
        PageResultVo<List<EventModelVO>> vo = new PageResultVo<>();
        if (ObjectUtils.isEmpty(dataList)) {
            dataList = new ArrayList<>();
            vo.setTotal(0);
        } else {
            PageInfo<AppConfigActivityData> pageInfo = new PageInfo<>(dataList);
            vo.setTotal(Math.toIntExact(pageInfo.getTotal()));
        }
        PageHelper.clearPage();
        String finalActiveStatus = activeStatus;
        List<EventModelVO> list = dataList.stream().map(data -> this.fillEventModelVO(data, finalActiveStatus, currTime))
                .collect(Collectors.toList());

        vo.setData(list);
        return vo;
    }

    private EventModelVO fillEventModelVO(AppConfigActivityData data, String finalActiveStatus, long currTime) {
        if (StringUtils.isEmpty(finalActiveStatus)) {
            if (currTime < data.getStartTime()) {
                finalActiveStatus = "未开始";
            } else if (currTime < data.getEndTime()) {
                finalActiveStatus = "进行中";
            } else {
                finalActiveStatus = "已结束";
            }
        }
        return new EventModelVO().setId(data.getId())
                .setEventCode(data.getActivityCode())
                .setEventGroup(data.getEventGroup())
                .setName(data.getName())
                .setEventDesc(data.getActivityDesc())
                .setZoneOffset(data.getZoneOffset())
                .setDateFormat(data.getDateFormat())
                .setStartTime(DayTimeSupport.secFormatStr(data.getStartTime(), data.getZoneOffset(), data.getDateFormat()))
                .setEndTime(DayTimeSupport.secFormatStr(data.getEndTime(), data.getZoneOffset(), data.getDateFormat()))
                .setChannelSet(data.getChannelSet())
                .setCountryCodeSet(data.getCountryCodeSet())
                .setUrl(data.getUrl())
                .setJoinModeInfo(data.getJoinModeInfo())
                .setNotJoinRidSet(data.getNotJoinRidSet())
                .setEventModeInfo(data.getEventModeInfo())
                .setRankType(data.getRankType())
                .setPageInfo(data.getPageInfo())
                .setValid(data.getValid())
                .setCtime(DayTimeSupport.secFormatStr(data.getCtime(), data.getZoneOffset(), data.getDateFormat()))
                .setMtime(DayTimeSupport.secFormatStr(data.getMtime(), data.getZoneOffset(), data.getDateFormat()))
                .setActiveStatus(finalActiveStatus)
                .setOperator(data.getOperator());
    }


    public List<RankRowVO> queryRankData(Integer eventCode) {
        if (eventCode == null) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("eventCode is empty"), true);
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(eventCode, -1);
        if (configData == null) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("not found this event,eventCode=" + eventCode), true);
        }

        List<ActivityCountActorInfoData> dataList = activityCountActorInfoDao.getListByEventCodeAndCountGroupFromRedis(eventCode, "", 100);
        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>(0);
        }
        return IntStream.range(0, dataList.size())
                .mapToObj(index -> this.fillRankRowVO(index, dataList.get(index)))
                .collect(Collectors.toList());
    }

    private RankRowVO fillRankRowVO(int index, ActivityCountActorInfoData data) {
        int rankNum = index + 1;
        return new RankRowVO()
                .setRankNum(rankNum)
                .setRid(data.getRid())
                .setUid(data.getUid())
                .setRankCount(data.getCount());
    }

    public CopyModelVO copyEvent(Integer eventCode) {
        if (eventCode == null) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("eventCode is empty"), true);
        }
        AppConfigActivityData configData = appConfigActivityDao.getOneByCodeAndValid(eventCode, -1);
        if (configData == null) {
            throw new WebException(new BaseHttpData(), new HttpCode(HttpCode.PARAM_ERROR).setMsg("not found this event,eventCode=" + eventCode), true);
        }
        return new CopyModelVO()
                .setName(configData.getName())
                .setEventDesc(configData.getActivityDesc())
                .setZoneOffset(configData.getZoneOffset())
                .setDateFormat(configData.getDateFormat())
                .setStartTime(DayTimeSupport.secFormatStr(configData.getStartTime(), configData.getZoneOffset(), configData.getDateFormat()))
                .setEndTime(DayTimeSupport.secFormatStr(configData.getEndTime(), configData.getZoneOffset(), configData.getDateFormat()))
                .setChannelSet(configData.getChannelSet())
                .setCountryCodeSet(configData.getCountryCodeSet())
                .setJoinModeInfo(configData.getJoinModeInfo())
                .setNotJoinRidSet(configData.getNotJoinRidSet())
                .setEventModeInfo(configData.getEventModeInfo())
                .setRankType(configData.getRankType())
                .setPageInfo(configData.getPageInfo())
                .setValid(configData.getValid());
    }
}
