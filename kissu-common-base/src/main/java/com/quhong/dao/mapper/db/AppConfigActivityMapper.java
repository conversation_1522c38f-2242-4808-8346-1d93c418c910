package com.quhong.dao.mapper.db;

import com.quhong.dao.datas.app.config.AppConfigActivityData;
import org.apache.ibatis.annotations.Param;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-05  16:41
 */
public interface AppConfigActivityMapper extends Mapper<AppConfigActivityData> {

    List<AppConfigActivityData> queryListByCondition(AppConfigActivityData dto);

    int updateOverGroupEventToInvalid(@Param("limitTime") long limitTime,
                                      @Param("currTime") long currTime,
                                      @Param("group") int group);
}
