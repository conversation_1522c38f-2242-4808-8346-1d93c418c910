package com.quhong.dao;

import com.quhong.common.data.HttpEnvData;
import com.quhong.common.enums.HttpCode;
import com.quhong.dao.datas.app.config.AppConfigActivityData;
import com.quhong.dao.mapper.db.AppConfigActivityMapper;
import com.quhong.data.bo.activity.model.QueryEventConfigBO;
import com.quhong.exceptions.WebException;
import com.quhong.redis.BaseHashSaveRedis;
import com.quhong.utils.TKUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import tk.mybatis.mapper.entity.Example;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-12-05  16:40
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class AppConfigActivityDao {
    private static final String ACTIVE_EVENT_KEY = "hash:active_event_list";

    private final AppConfigActivityMapper mapper;

    private final BaseHashSaveRedis baseHashSaveRedis;

    @CacheEvict(value = "app_config_activity", key = "#data.activityCode")
    public boolean insertOneSelective(AppConfigActivityData data) {
        int code = mapper.insertSelective(data);
        if (code <= 0) {
            return false;
        }
        baseHashSaveRedis.deleteByKey(ACTIVE_EVENT_KEY);
        return true;
    }

    @CacheEvict(value = "app_config_activity", key = "#data.activityCode")
    public boolean updateOneSelective(AppConfigActivityData data) {
        int code = mapper.updateByPrimaryKeySelective(data);
        if (code <= 0) {
            return false;
        }
        baseHashSaveRedis.deleteByKey(ACTIVE_EVENT_KEY);
        return true;
    }

    /**
     * 活动分组活动过期设置为无效
     *
     */
    public void updateOverGroupEventToInvalid(long limitTime, long currTime, int group) {
        int code = mapper.updateOverGroupEventToInvalid(limitTime, currTime, group);
        if (code <= 0) {
            return;
        }
        baseHashSaveRedis.deleteByKey(ACTIVE_EVENT_KEY);
        getListByGroupAndValid(group, 1);
    }

    @Cacheable(value = "app_config_activity", key = "#eventCode")
    public AppConfigActivityData getOneByEventCodeThrowWebException(Integer eventCode) throws WebException {
        AppConfigActivityData configData = getOneByCodeAndValid(eventCode, 1);
        if (configData == null) {
            throw new WebException(new HttpEnvData(), new HttpCode(HttpCode.OTHER_NOT_EXISTS));
        }
        return configData;
    }

    @Cacheable(value = "app_config_activity", key = "#eventCode")
    public AppConfigActivityData getValidOneByEventCode(Integer eventCode) throws WebException{
        return getOneByCodeAndValid(eventCode, 1);
    }

    @Cacheable(value = "app_config_activity", key = "#code + ':' + #valid")
    public AppConfigActivityData getOneByCodeAndValid(int code, int valid) {
        Example example = TKUtils.creatExample(AppConfigActivityData.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("activityCode", code);
        if (valid != -1) {
            criteria.andEqualTo("valid", valid);
        }
        return mapper.selectOneByExample(example);
    }

    public List<AppConfigActivityData> getListByGroupAndValid(int group, int valid) {
        QueryEventConfigBO bo = new QueryEventConfigBO().setValid(valid).setEventGroup(group);
        return baseHashSaveRedis.getListByRedis(bo,
                ACTIVE_EVENT_KEY,
                AppConfigActivityData.class,
                this::getListByGroupAndValidFromDb,
                Duration.ofDays(2));
    }

    public List<AppConfigActivityData> getListByGroupAndValidFromDb(QueryEventConfigBO bo) {
        Example example = TKUtils.creatExample(AppConfigActivityData.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("eventGroup", bo.getEventGroup());
        if (bo.getValid() != -1) {
            criteria.andEqualTo("valid", bo.getValid());
        }
        return mapper.selectByExample(example);
    }

    public List<AppConfigActivityData> queryListBy(String zoneOffset, Integer valid, Integer eventGroup,
                                                        Long startGreaterThan, Long startLessThanAndEqualTo,
                                                        Long endGreaterThan, Long endLessThan) {
        Example example = TKUtils.creatExample(AppConfigActivityData.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("zoneOffset", zoneOffset);
        criteria.andEqualTo("eventGroup", eventGroup);
        if (valid != -1) {
            criteria.andEqualTo("valid", valid);
        }
        if (startGreaterThan != null) {
            criteria.andGreaterThan("startTime", startGreaterThan);
        }
        if (startLessThanAndEqualTo != null) {
            criteria.andLessThanOrEqualTo("startTime", startLessThanAndEqualTo);
        }
        if (endGreaterThan != null) {
            criteria.andGreaterThan("endTime", endGreaterThan);
        }
        if (endLessThan != null) {
            criteria.andLessThanOrEqualTo("endTime", endLessThan);
        }

        example.orderBy("startTime").desc();
        List<AppConfigActivityData> dataList = mapper.selectByExample(example);
        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        return dataList;
    }

    public List<AppConfigActivityData> queryCurrActiveEvent(long currTime, int valid) {
        if (currTime == 0) {
            return new ArrayList<>();
        }
        Example example = TKUtils.creatExample(AppConfigActivityData.class);
        Example.Criteria criteria = example.createCriteria();
        if (valid != -1) {
            criteria.andEqualTo("valid", valid);
        }
        criteria.andLessThanOrEqualTo("startTime", currTime);
        criteria.andGreaterThan("endTime", currTime);
        List<AppConfigActivityData> dataList = mapper.selectByExample(example);
        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        return dataList;
    }

    public List<AppConfigActivityData> getListByCondition(AppConfigActivityData dto) {
        return mapper.queryListByCondition(dto);
    }
}
