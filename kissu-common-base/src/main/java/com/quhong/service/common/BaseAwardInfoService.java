package com.quhong.service.common;

import com.quhong.common.enums.HttpCode;
import com.quhong.common.utils.CdnUtils;
import com.quhong.constant.AppConfigKeyConstant;
import com.quhong.dao.*;
import com.quhong.dao.datas.GiftListConfigData;
import com.quhong.dao.datas.ResourceConfigData;
import com.quhong.dao.datas.common.AwardInfo;
import com.quhong.dao.datas.db.LordConfigData;
import com.quhong.dao.datas.db.MedalConfigData;
import com.quhong.dao.datas.db.RoomItemsConfigData;
import com.quhong.data.appConfig.StarryCarnivalTicketConfig;
import com.quhong.data.dto.ConfigDTO;
import com.quhong.enums.RewardItemType;
import com.quhong.exceptions.WebException;
import com.quhong.service.ConfigApi;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2025/4/17 14:59
 */
@Lazy
@Slf4j
@Component
@RequiredArgsConstructor
public class BaseAwardInfoService {
    private final GiftListConfigDao giftListConfigDao;
    private final ResourceConfigDao resourceConfigDao;
    private final RoomItemsConfigDao roomItemsConfigDao;
    private final LordConfigDao lordConfigDao;
    private final MedalConfigDao medalConfigDao;
    private final ConfigApi configApi;

    public AwardInfo findAwardInfo(Integer awardType, Integer dataId) {
        AwardInfo awardInfo = new AwardInfo();
        awardInfo.setAwardType(awardType);
        awardInfo.setDataId(dataId);

        // 根据奖励类型不同，获取不同的物品信息
        switch (awardType) {
            case RewardItemType.GOLD: // 金币
                dealGoldInfo(awardInfo);
                break;
            case RewardItemType.GIFT: // 礼物
                dealGiftInfo(dataId, awardInfo);
                break;
            case RewardItemType.MATCH_CARD: // 匹配卡
                dealMatchCardInfo(awardInfo);
                break;
            case RewardItemType.VIP_DAYS: // VIP天数
                dealVIPInfo(awardInfo);
                break;
            case RewardItemType.LORD_DAYS: // 贵族天数
                dealLordInfo(dataId, awardInfo);
                break;
            case RewardItemType.MEDAL: // 勋章
                dealMedalInfo(dataId, awardInfo);
                break;
            case RewardItemType.DIAMOND: // 钻石
                dealDiamondInfo(awardInfo);
                break;
            case RewardItemType.LEVEL_SCORE: // 等级积分
                dealLevelScoreInfo(awardInfo);
                break;
            case RewardItemType.STARRY_TICKET: // 星动嘉年华抽奖券
                dealStarryTicketInfo(awardInfo);
                break;
            case RewardItemType.SEAT_FRAME:  // 麦位框
            case RewardItemType.BUBBLE_FRAME: // 气泡框
            case RewardItemType.ENTER_EFFECT: // 座驾
            case RewardItemType.ENTRY_EFFECT: // 进场特效
            case RewardItemType.DESIGNATION: //称号
            case RewardItemType.HOST_LABEL: // 官方推荐标签
            case RewardItemType.PROFILE_CARD: //资料卡披肩
                dealRoomItemInfo(dataId, awardInfo, awardType);
                break;
            default:
                awardInfo.setAwardName("");
                awardInfo.setAwardIcon("");
                awardInfo.setUnitPrice(0);
                break;
        }

        return awardInfo;
    }

    private void dealRoomItemInfo(Integer dataId, AwardInfo awardInfo, int awardType) {
        RoomItemsConfigData roomItemConfig = roomItemsConfigDao.getDataByItemId(dataId);
        if (roomItemConfig == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "物品不存在，itemId=" + dataId), true);
        }
        ResourceConfigData itemSourceData = resourceConfigDao.getResourceByResourceId(roomItemConfig.getResourceId());
        if (itemSourceData == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "物品资源不存在，resourceId=" + roomItemConfig.getResourceId()), true);
        }
        int roomItemType = awardType % 1000;
        if (roomItemConfig.getRoomItemType() != roomItemType) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "物品类型不匹配"), true);
        }
        awardInfo.setAwardName(roomItemConfig.getRoomItemName());
        awardInfo.setAwardIcon(itemSourceData.getIconUrl());
    }

    private void dealStarryTicketInfo(AwardInfo awardInfo) {
        StarryCarnivalTicketConfig.TicketConfig ticketConfig = findTicketConfig(awardInfo.getDataId());
        if (ticketConfig == null) {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "奖券不存在，itemId=" + awardInfo.getDataId()), true);
        }
        awardInfo.setAwardName("");
        awardInfo.setAwardIcon(ticketConfig.getIcon());
    }


    private StarryCarnivalTicketConfig.TicketConfig findTicketConfig(int ticketId) {
        StarryCarnivalTicketConfig config = configApi.getJavaBeanVal(new ConfigDTO("", AppConfigKeyConstant.STARRY_CARNIVAL_TICKET_CONFIG, AppConfigKeyConstant.STATUS_SERVER), StarryCarnivalTicketConfig.class);
        if (CollectionUtils.isEmpty(config.getConfigList())) {
            return null;
        }
        return config.getConfigList().stream()
                .filter(data -> data.getTicketId() == ticketId)
                .findFirst()
                .orElse(null);
    }

    private static void dealLevelScoreInfo(AwardInfo awardInfo) {
        awardInfo.setAwardName("Level Score");
        awardInfo.setUnitPrice(0);
    }

    private static void dealDiamondInfo(AwardInfo awardInfo) {
        awardInfo.setAwardName("Diamond");
        awardInfo.setAwardIcon(RewardItemType.DIAMOND_ICON);
        awardInfo.setUnitPrice(10);
    }

    private void dealMedalInfo(Integer dataId, AwardInfo awardInfo) {
        MedalConfigData medalConfig = medalConfigDao.getMedalConfigByMedalId(dataId, "");
        if (medalConfig != null) {
            String icon = CdnUtils.DOMAIN_4 + medalConfig.getMedalIconPath();
            awardInfo.setAwardName(medalConfig.getMedalName());
            awardInfo.setAwardIcon(icon);
        }else {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "勋章不存在，medalId=" + dataId), true);
        }
    }

    private void dealLordInfo(Integer dataId, AwardInfo awardInfo) {
        LordConfigData lordConfig = lordConfigDao.getLordConfigByLevel(dataId);
        if (lordConfig != null) {
            awardInfo.setAwardName(lordConfig.getLevelName());
            awardInfo.setAwardIcon(lordConfig.getLevelIcon());
        }else {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "贵族不存在，level=" + dataId), true);
        }
    }

    private static void dealVIPInfo(AwardInfo awardInfo) {
        awardInfo.setAwardName("VIP");
        awardInfo.setAwardIcon(RewardItemType.VIP_ICON);
    }

    private static void dealMatchCardInfo(AwardInfo awardInfo) {
        awardInfo.setAwardName("Call Card");
        awardInfo.setAwardIcon(RewardItemType.CALL_CARD_ICON);
        awardInfo.setUnitPrice(10);
    }

    private void dealGiftInfo(Integer dataId, AwardInfo awardInfo) {
        GiftListConfigData giftConfig = giftListConfigDao.getGiftConfig(dataId);
        if (giftConfig != null) {
            ResourceConfigData resourceData = resourceConfigDao.getResourceByResourceId(giftConfig.getResourceId());
            if (resourceData != null) {
                awardInfo.setAwardName(giftConfig.getGiftName());
                awardInfo.setAwardIcon(resourceData.getIconUrl());
                awardInfo.setUnitPrice(giftConfig.getPrice());
            } else {
                throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "礼物资源不存在，resourceId=" + giftConfig.getResourceId()), true);
            }
        } else {
            throw new WebException(HttpCode.createHttpCode(HttpCode.OTHER_NOT_EXISTS, false, "礼物不存在，giftId=" + dataId), true);
        }
    }

    private static void dealGoldInfo(AwardInfo awardInfo) {
        awardInfo.setAwardName("Coins");
        awardInfo.setAwardIcon(RewardItemType.GOLD_ICON);
        awardInfo.setUnitPrice(1);
    }
}
